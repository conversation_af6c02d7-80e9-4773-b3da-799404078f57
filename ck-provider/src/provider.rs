use crate::config::CkConfig;
use crate::error::CkProviderError;
use anyhow::anyhow;
use async_trait::async_trait;
use clickhouse::{Client, Row};
use serde::{de::DeserializeOwned, Deserialize, Serialize};
use std::time::Instant;

/// ClickHouse provider trait
#[async_trait]
pub trait CkProvider {
    /// Execute query and return collection of structs
    async fn query<T>(&self, query: &str) -> Result<Vec<T>, CkProviderError>
    where
        T: Row + DeserializeOwned + Send + 'static;

    /// Execute insert operation
    async fn insert<T>(&self, table: &str, data: &[T]) -> Result<(), CkProviderError>
    where
        T: Row + Serialize + Send + Sync + 'static;

    /// Execute SQL statement
    async fn execute(&self, query: &str) -> Result<(), CkProviderError>;

    /// Execute count query
    async fn count(&self, query: &str) -> Result<Option<i64>, CkProviderError>;

    /// Get client reference
    fn get_client(&self) -> &Client;
}

/// ClickHouse provider implementation
#[derive(Clone)]
pub struct CkProviderImpl {
    client: Client,
    config: CkConfig,
}

impl CkProviderImpl {
    /// Create new ClickHouse client
    pub fn new(config: CkConfig) -> Self {
        // TODO fix
        let url = config.url.replace("jdbc:clickhouse://", "http://");
        log::info!("clickhouse url: {}", url);
        let mut client = Client::default()
            .with_url(&url)
            .with_user(&config.username)
            .with_password(&config.password)
            .with_database(&config.database)
            .with_option("connect_timeout", config.timeout.as_secs().to_string());
        if config.compression {
            client = client.with_compression(clickhouse::Compression::Lz4);
        }

        Self { client, config }
    }
}

#[async_trait]
impl CkProvider for CkProviderImpl {
    /// Execute query and return collection of structs
    async fn query<T>(&self, query: &str) -> Result<Vec<T>, CkProviderError>
    where
        T: Row + DeserializeOwned + Send + 'static,
    {
        log::info!("读取ck开始, {}", query);
        let start = Instant::now();

        let result = self.client.query(query).fetch_all::<T>().await.map_err(|e| {
            let err_str = e.to_string();
            log::error!("读取ck失败: {}", anyhow!(e));
            CkProviderError::ExecutionError(err_str)
        })?;

        log::info!("读取ck完成，耗时：{:?}, dataSize: {}", start.elapsed(), result.len());
        Ok(result)
    }

    /// Execute insert operation
    async fn insert<T>(&self, table: &str, data: &[T]) -> Result<(), CkProviderError>
    where
        T: Row + Serialize + Send + Sync + 'static,
    {
        if data.is_empty() {
            return Ok(());
        }

        log::info!("数据写ck开始, dataSize: {}", data.len());
        let start = Instant::now();

        // Create insert statement
        let insert_result = self.client.insert(table);
        if let Err(e) = &insert_result {
            return Err(CkProviderError::ExecutionError(e.to_string()));
        }
        let mut insert = insert_result.unwrap();

        // Write data in batches
        for chunk in data.chunks(self.config.batch_size) {
            for item in chunk {
                insert
                    .write(item)
                    .await
                    .map_err(|e| CkProviderError::ExecutionError(e.to_string()))?;
            }
        }

        // Complete insert
        insert.end().await.map_err(|e| CkProviderError::ExecutionError(e.to_string()))?;

        log::info!("数据写ck完成，耗时：{:?}", start.elapsed());
        Ok(())
    }

    /// Execute SQL statement
    async fn execute(&self, query: &str) -> Result<(), CkProviderError> {
        log::info!("执行ck开始 {}", query);
        let start = Instant::now();

        self.client.query(query).execute().await.map_err(|e| {
            let err_str = e.to_string();
            let msg = format!("{:#}", anyhow!(e));
            log::error!("{}", msg);
            CkProviderError::ExecutionError(err_str)
        })?;

        log::info!("执行ck完成，耗时：{:?}", start.elapsed());
        Ok(())
    }

    /// Execute count query
    async fn count(&self, query: &str) -> Result<Option<i64>, CkProviderError> {
        log::info!("读取ck开始, {}", query);
        let start = Instant::now();
        #[derive(Row, Deserialize, Debug)]
        struct Count {
            #[serde(rename = "")]
            count: i64,
        }

        let result = self
            .client
            .query(query)
            .fetch_all::<Count>()
            .await
            .map_err(|e| CkProviderError::ExecutionError(e.to_string()))?;

        let count = if result.is_empty() { None } else { Some(result[0].count) };

        log::info!("读取ck完成，耗时：{:?}, count = {:?}", start.elapsed(), count);
        Ok(count)
    }

    /// Get client reference
    fn get_client(&self) -> &Client {
        &self.client
    }
}

/// ClickHouse query row iterator
pub struct CkRowIterator<T>
where
    T: Row + DeserializeOwned + Send + 'static,
{
    items: Vec<Option<T>>,
    index: usize,
}

impl<T> CkRowIterator<T>
where
    T: Row + DeserializeOwned + Send + 'static,
{
    pub fn new(items: Vec<T>) -> Self {
        // Wrap each element as Option<T>
        let items = items.into_iter().map(Some).collect();
        Self { items, index: 0 }
    }

    pub async fn next(&mut self) -> Result<Option<T>, CkProviderError> {
        if self.index < self.items.len() {
            let item = self.items[self.index].take();
            self.index += 1;
            Ok(item)
        } else {
            Ok(None)
        }
    }
}

/// Extended CkProvider interface with iterator query methods
#[async_trait]
pub trait CkProviderExt: CkProvider {
    /// Query data using iterator pattern
    async fn query_iter<T>(&self, query: &str) -> Result<CkRowIterator<T>, CkProviderError>
    where
        T: Row + DeserializeOwned + Send + 'static;
}

#[async_trait]
impl CkProviderExt for CkProviderImpl {
    async fn query_iter<T>(&self, query: &str) -> Result<CkRowIterator<T>, CkProviderError>
    where
        T: Row + DeserializeOwned + Send + 'static,
    {
        log::info!("迭代读取ck开始, {}", query);
        let items = self.query::<T>(query).await?;
        Ok(CkRowIterator::new(items))
    }
}

/// Implement parallel write functionality, similar to splitList and writeToCk in CkProvider.scala
pub async fn write_to_ck_parallel<T>(
    provider: &impl CkProvider,
    table: &str,
    data: &[T],
    parallelism: usize,
) -> Result<(), CkProviderError>
where
    T: Row + Serialize + Send + Sync + Clone + 'static,
{
    if data.is_empty() {
        return Ok(());
    }

    log::info!("数据并行写ck开始, dataSize: {}", data.len());
    let start = Instant::now();

    if parallelism <= 1 {
        provider.insert(table, data).await?;
    } else {
        // Split data into parallelism parts
        let chunk_size = (data.len() + parallelism - 1) / parallelism;
        let mut tasks = Vec::with_capacity(parallelism);

        for chunk in data.chunks(chunk_size) {
            let chunk_data = chunk.to_vec();
            let client = provider.get_client().clone();
            let table_name = table.to_string();

            // Create async task
            let task = tokio::spawn(async move {
                // Create insert statement
                let insert_result = client.insert(&table_name);
                if let Err(e) = &insert_result {
                    return Err(CkProviderError::ExecutionError(e.to_string()));
                }
                let mut insert = insert_result.unwrap();

                for item in &chunk_data {
                    insert
                        .write(item)
                        .await
                        .map_err(|e| CkProviderError::ExecutionError(e.to_string()))?;
                }

                insert.end().await.map_err(|e| CkProviderError::ExecutionError(e.to_string()))?;

                Ok::<_, CkProviderError>(())
            });

            tasks.push(task);
        }

        // Wait for all tasks to complete
        for task in tasks {
            match task.await {
                Ok(result) => result?,
                Err(e) => return Err(CkProviderError::ExecutionError(e.to_string())),
            }
        }
    }

    log::info!("数据并行写ck完成，耗时：{:?}", start.elapsed());
    Ok(())
}
